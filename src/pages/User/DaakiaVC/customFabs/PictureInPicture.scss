// PictureInPicture SCSS - Clean styling for PiP components
// Easy to modify and extend

// Base styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: #1a1a1a;
  font-family: Arial, sans-serif;
  overflow: hidden;
  width: 100vw;
  height: 100vh;
}

// Main container
.pip-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

// Header section
.pip-header {
  background: #2c2c2c;
  color: white;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: bold;
  flex-shrink: 0;
  z-index: 10;
}

// Content area
.pip-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

// Video display
.pip-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #000;
}

// Avatar display
.pip-avatar-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: white;
  text-align: center;
  background: #1a1a1a;
}

.pip-avatar-inner {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pip-avatar-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #555;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 12px;
}

.pip-avatar-name {
  font-size: 14px;
  margin-bottom: 4px;
}

.pip-avatar-status {
  font-size: 10px;
  opacity: 0.7;
}

// Controls section
.pip-controls {
  height: 60px;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px;
  flex-shrink: 0;
  z-index: 10;
}

// Button styles
.pip-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: white;

  &:hover {
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.9);
  }

  svg {
    width: 16px;
    height: 16px;
    fill: currentColor;
  }
}

// Specific button types
.pip-mic {
  &.active {
    background: #4CAF50;
  }
  
  &.muted {
    background: #f44336;
  }
}

.pip-camera {
  &.active {
    background: #2196F3;
  }
  
  &.muted {
    background: #f44336;
  }
}

.pip-screen {
  &.active {
    background: #4CAF50;
  }
  
  &.inactive {
    background: #FF9800;
  }
}

.pip-end {
  background: #f44336;
}

// Responsive adjustments
@media (max-width: 400px) {
  .pip-header {
    font-size: 12px;
    padding: 6px 10px;
  }

  .pip-controls {
    height: 50px;
    gap: 6px;
  }

  .pip-btn {
    width: 32px;
    height: 32px;

    svg {
      width: 14px;
      height: 14px;
    }
  }

  .pip-avatar-circle {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }

  .pip-avatar-name {
    font-size: 12px;
  }

  .pip-avatar-status {
    font-size: 9px;
  }
}

// Animation for smooth transitions
.pip-container {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// Focus styles for accessibility
.pip-btn:focus {
  outline: 2px solid #fff;
  outline-offset: 2px;
}

// Loading state (you can add this later)
.pip-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: white;
  font-size: 14px;
}
