import { useRef, useState, useEffect } from 'react';
import { createRoot } from 'react-dom/client';
import { Track } from 'livekit-client';
import './PictureInPicture.scss';

// Clean JSX-based PiP with SCSS styling
// Simple structure for easy feature additions

// Control Button Components
function MicButton({ room }) {
  const [isMuted, setIsMuted] = useState(false);

  useEffect(() => {
    const updateState = () => {
      const micTrack = room.localParticipant.getTrackPublication(Track.Source.Microphone);
      setIsMuted(micTrack?.isMuted ?? true);
    };

    updateState();
    room.on('trackMuted', updateState);
    room.on('trackUnmuted', updateState);

    return () => {
      room.off('trackMuted', updateState);
      room.off('trackUnmuted', updateState);
    };
  }, [room]);

  const handleClick = () => {
    const micTrack = room.localParticipant.getTrackPublication(Track.Source.Microphone);
    room.localParticipant.setMicrophoneEnabled(micTrack?.isMuted ?? true);
  };

  return (
    <button
      className={`pip-btn pip-mic ${isMuted ? 'muted' : 'active'}`}
      onClick={handleClick}
      aria-label={isMuted ? "Unmute microphone" : "Mute microphone"}
    >
      <svg viewBox="0 0 24 24" aria-hidden="true">
        {isMuted ? (
          <path d="M19 11h-1.7c0 .74-.16 1.43-.43 2.05l1.23 1.23c.56-.98.9-2.09.9-3.28zm-4.02.17c0-.06.02-.11.02-.17V5c0-1.66-1.34-3-3-3S9 3.34 9 5v.18l5.98 5.99zM4.27 3L3 4.27l6.01 6.01V11c0 1.66 1.33 3 2.99 3 .22 0 .44-.03.65-.08l1.66 1.66c-.71.33-1.5.52-2.31.52-2.76 0-5.3-2.1-5.3-5.1H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c.91-.13 1.77-.45 2.54-.9L19.73 21 21 19.73 4.27 3z"/>
        ) : (
          <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
        )}
      </svg>
    </button>
  );
}

function CameraButton({ room, onToggle }) {
  const [isMuted, setIsMuted] = useState(false);

  useEffect(() => {
    const updateState = () => {
      const cameraTrack = room.localParticipant.getTrackPublication(Track.Source.Camera);
      setIsMuted(cameraTrack?.isMuted ?? true);
    };

    updateState();
    room.on('trackMuted', updateState);
    room.on('trackUnmuted', updateState);

    return () => {
      room.off('trackMuted', updateState);
      room.off('trackUnmuted', updateState);
    };
  }, [room]);

  const handleClick = () => {
    const cameraTrack = room.localParticipant.getTrackPublication(Track.Source.Camera);
    room.localParticipant.setCameraEnabled(cameraTrack?.isMuted ?? true);
    if (onToggle) onToggle();
  };

  return (
    <button
      className={`pip-btn pip-camera ${isMuted ? 'muted' : 'active'}`}
      onClick={handleClick}
      aria-label={isMuted ? "Turn on camera" : "Turn off camera"}
    >
      <svg viewBox="0 0 24 24" aria-hidden="true">
        {isMuted ? (
          <path d="M21 6.5l-4 4V7c0-.55-.45-1-1-1H9.82L21 17.18V6.5zM3.27 2L2 3.27 4.73 6H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.21 0 .39-.08.54-.18L19.73 21 21 19.73 3.27 2zM5 16V8h1.73l8 8H5z"/>
        ) : (
          <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z"/>
        )}
      </svg>
    </button>
  );
}

function ScreenShareButton({ room, meetingFeatures, onToggle }) {
  const [isSharing, setIsSharing] = useState(false);

  useEffect(() => {
    const updateState = () => {
      const screenTrack = room.localParticipant.getTrackPublication(Track.Source.ScreenShare);
      setIsSharing(screenTrack && !screenTrack.isMuted);
    };

    updateState();
    room.on('trackPublished', updateState);
    room.on('trackUnpublished', updateState);

    return () => {
      room.off('trackPublished', updateState);
      room.off('trackUnpublished', updateState);
    };
  }, [room]);

  const handleClick = async () => {
    try {
      const screenTrack = room.localParticipant.getTrackPublication(Track.Source.ScreenShare);
      if (screenTrack && !screenTrack.isMuted) {
        await room.localParticipant.setScreenShareEnabled(false);
      } else {
        await room.localParticipant.setScreenShareEnabled(true);
      }
      if (onToggle) onToggle();
    } catch (error) {
      console.error('Screen share error:', error);
    }
  };

  if (meetingFeatures?.screen_sharing !== 1) return null;

  return (
    <button
      className={`pip-btn pip-screen ${isSharing ? 'active' : 'inactive'}`}
      onClick={handleClick}
      aria-label={isSharing ? "Stop screen sharing" : "Start screen sharing"}
    >
      <svg viewBox="0 0 24 24" aria-hidden="true">
        <path d="M20 18c1.1 0 1.99-.9 1.99-2L22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2H0v2h24v-2h-4zM4 6h16v10H4V6z"/>
      </svg>
    </button>
  );
}

function EndCallButton({ room, isElectronApp, onClose }) {
  const handleClick = () => {
    if (isElectronApp) {
      window?.electronAPI?.ipcRenderer?.send("stop-annotation");
    }
    room.disconnect();
    onClose();
  };

  return (
    <button
      className="pip-btn pip-end"
      onClick={handleClick}
      aria-label="End call"
    >
      <svg viewBox="0 0 24 24" aria-hidden="true">
        <path d="M12 9c-1.6 0-3.15.25-4.6.72v3.1c0 .39-.23.74-.56.9-.98.49-1.87 1.12-2.66 1.85-.18.18-.43.28-.7.28-.28 0-.53-.11-.71-.29L.29 13.08c-.18-.17-.29-.42-.29-.7 0-.28.11-.53.29-.71C3.34 8.78 7.46 7 12 7s8.66 1.78 11.71 4.67c.***********.29.71 0 .28-.11.53-.29.7l-2.48 2.48c-.18.18-.43.29-.71.29-.27 0-.52-.1-.7-.28-.79-.73-1.68-1.36-2.66-1.85-.33-.16-.56-.51-.56-.9v-3.1C15.15 9.25 13.6 9 12 9z"/>
      </svg>
    </button>
  );
}

// Clean JSX Layout Components
function PipControls({ room, meetingFeatures, isElectronApp, onClose, onCameraToggle }) {
  return (
    <div className="pip-controls">
      <MicButton room={room} />
      <CameraButton room={room} onToggle={onCameraToggle} />
      <ScreenShareButton room={room} meetingFeatures={meetingFeatures} onToggle={onCameraToggle} />
      <EndCallButton room={room} isElectronApp={isElectronApp} onClose={onClose} />
    </div>
  );
}

function PipContent({ children }) {
  return (
    <div className="pip-content" id="pip-content">
      {children}
    </div>
  );
}

function PipHeader({ title = "Daakia" }) {
  return (
    <div className="pip-header">
      {title}
    </div>
  );
}

function PipContainer({ children }) {
  return (
    <div className="pip-container">
      {children}
    </div>
  );
}

function VideoDisplay({ track }) {
  return (
    <video
      className="pip-video"
      autoPlay
      playsInline
      muted
      ref={(video) => {
        if (video && track) {
          const stream = new MediaStream([track.mediaStreamTrack]);
          video.srcObject = stream;
          video.play().catch(e => console.log('Video play error:', e));
        }
      }}
    />
  );
}

function AvatarDisplay({ name, avatarText, status }) {
  return (
    <div className="pip-avatar-container">
      <div className="pip-avatar-inner">
        <div className="pip-avatar-circle">
          {avatarText}
        </div>
        <div className="pip-avatar-name">{name}</div>
        <div className="pip-avatar-status">{status}</div>
      </div>
    </div>
  );
}

// PiP functionality as a custom hook
export const usePictureInPicture = (
  room,
  tracks,
  isTrackReference,
  generateAvatar,
  setIsPIPEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast,
  meetingFeatures,
  isElectronApp
) => {
  const pipWindowRef = useRef(null);

  // Simplified content update using React components
  const updatePipContent = () => {
    if (!pipWindowRef.current) return;

    const pipDoc = pipWindowRef.current.document;
    const pipContent = pipDoc.getElementById('pip-content');
    if (!pipContent) return;

    // Find current track to show
    let trackToShow = null;

    // Priority 1: Screen share (any participant)
    const screenShareTracks = tracks
      .filter(isTrackReference)
      .filter((track) => track.publication.source === Track.Source.ScreenShare);

    if (screenShareTracks.length > 0 && screenShareTracks[0].publication.isSubscribed) {
      [trackToShow] = screenShareTracks;
    } else {
      // Priority 2: Local camera
      const localCameraTracks = tracks
        .filter(isTrackReference)
        .filter((track) =>
          track.publication.source === Track.Source.Camera &&
          track.participant.isLocal
        );

      if (localCameraTracks.length > 0) {
        [trackToShow] = localCameraTracks;
      }
    }

    // If no track, create dummy for avatar
    if (!trackToShow) {
      trackToShow = {
        participant: room.localParticipant,
        source: Track.Source.Camera,
        publication: null
      };
    }

    // Clear existing content
    while (pipContent.firstChild) {
      pipContent.removeChild(pipContent.firstChild);
    }

    // Create content using React components
    const contentRoot = createRoot(pipContent);

    if (trackToShow.publication && trackToShow.publication.track && !trackToShow.publication.isMuted) {
      // Show video
      contentRoot.render(
        <VideoDisplay track={trackToShow.publication.track} />
      );
    } else {
      // Show avatar
      const name = room.localParticipant.name || 'You';
      const avatarText = generateAvatar(name);
      const status = trackToShow.publication ? 'Camera Off' : 'No Camera';

      contentRoot.render(
        <AvatarDisplay
          name={name}
          avatarText={avatarText}
          status={status}
        />
      );
    }
  };

  const togglePipMode = async (enabled) => {
    // Check Document PiP support
    if (!('documentPictureInPicture' in window)) {
      setToastNotification("Document Picture-in-Picture not supported");
      setToastStatus("error");
      setShowToast(true);
      return;
    }

    // Close existing PiP
    if (pipWindowRef.current) {
      pipWindowRef.current.close();
      pipWindowRef.current = null;
      setIsPIPEnabled(false);
      return;
    }

    if (!enabled) return;

    try {
      // Create PiP window with size for controls
      const pipWindow = await window.documentPictureInPicture.requestWindow({
        width: 320,
        height: 300,
      });

      pipWindowRef.current = pipWindow;
      setIsPIPEnabled(true);

      // Setup PiP document with clean JSX structure
      const pipDoc = pipWindow.document;

      // Add SCSS import link (styles will be in separate file)
      const link = pipDoc.createElement('link');
      link.rel = 'stylesheet';
      link.type = 'text/css';
      link.href = './PictureInPicture.scss'; // You'll create this file
      pipDoc.head.appendChild(link);

      // Create main container and render React layout
      const mainRoot = pipDoc.createElement('div');
      mainRoot.id = 'pip-root';
      pipDoc.body.appendChild(mainRoot);

      // Render the complete PiP layout using React
      const pipRoot = createRoot(mainRoot);
      pipRoot.render(
        <PipContainer>
          <PipHeader title="Daakia" />
          <PipContent />
          <PipControls
            room={room}
            meetingFeatures={meetingFeatures}
            isElectronApp={isElectronApp}
            onClose={() => pipWindow.close()}
            onCameraToggle={() => setTimeout(updatePipContent, 100)}
          />
        </PipContainer>
      );

      // Initial render - use updatePipContent function
      updatePipContent();

      // Handle window close
      pipWindow.addEventListener('pagehide', () => {
        pipWindowRef.current = null;
        setIsPIPEnabled(false);
      });

    } catch (error) {
      console.error('PiP error:', error);
      setToastNotification("Failed to create Picture-in-Picture");
      setToastStatus("error");
      setShowToast(true);
    }
  };

  return {
    togglePipMode,
    updatePipContent,
    pipWindowRef
  };
};
